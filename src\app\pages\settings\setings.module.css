/* Main Container */
.rightContainerBody {
    width: 100%;
    height: 100%;
    padding: 30px;
    border-radius: 20px;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: calc(100vh - 120px);
}

@media screen and (max-width: 576px) {
    .rightContainerBody {
        padding: 15px 5px;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
    }
}

/* Settings Container */
.settingsContainer {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

@media screen and (max-width: 576px) {
    .settingsContainer {
        width: 98%;
        gap: 20px;
    }
}

/* Mobile Header Section */
.mobileHeader {
    display: none;
}

@media (max-width: 576px) {
    .mobileHeader {
        display: block;
        margin-bottom: 20px;
        padding: 0 16px;
        text-align: center;
    }
}

.headerContent {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.pageTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

@media screen and (max-width: 576px) {
    .pageTitle {
        font-size: 24px;
        text-align: center;
        width: 100%;
    }
}

.pageSubtitle {
    font-size: 14px;
    color: #64748B;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

@media (max-width: 576px) {
    .pageSubtitle {
        font-size: 13px;
        text-align: center;
    }
}

/* Settings Card */
.settingsCard {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

@media screen and (max-width: 576px) {
    .settingsCard {
        padding: 20px 15px;
        border-radius: 16px;
        margin-bottom: 16px;
    }
}

.settingsCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

/* Card Header */
.cardHeader {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .cardHeader {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 12px;
        margin-bottom: 20px;
    }
}

.cardIcon {
    font-size: 32px;
    opacity: 0.8;
    flex-shrink: 0;
}

@media screen and (max-width: 576px) {
    .cardIcon {
        font-size: 28px;
    }
}

.cardHeaderContent {
    flex: 1;
}

.cardTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

@media screen and (max-width: 576px) {
    .cardTitle {
        font-size: 20px;
        margin-bottom: 6px;
        text-align: center;
    }
}

.cardDescription {
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
}

@media screen and (max-width: 576px) {
    .cardDescription {
        font-size: 13px;
        text-align: center;
    }
}

/* Card Body */
.cardBody {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

@media screen and (max-width: 576px) {
    .cardBody {
        gap: 16px;
    }
}

/* Form Elements */
.formGroup {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
}

.formLabel {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 6px;
}

@media screen and (max-width: 576px) {
    .formLabel {
        font-size: 13px;
    }
}

.tooltipIcon {
    width: 16px;
    height: 16px;
    fill: #6b7280;
    cursor: help;
    transition: fill 0.2s ease;
}

.tooltipIcon:hover {
    fill: #3b82f6;
}

.formInput {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 14px;
    font-family: inherit;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
}

@media screen and (max-width: 576px) {
    .formInput {
        padding: 10px 14px;
        font-size: 13px;
    }
}

.formInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formInput::placeholder {
    color: #94a3b8;
}

/* Profile Section */
.profileMainSection {
    display: flex;
    gap: 32px;
    margin-bottom: 24px;
}

@media screen and (max-width: 576px) {
    .profileMainSection {
        flex-direction: column;
        align-items: center;
        gap: 24px;
    }
}

.profilePictureSection {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.profilePictureContainer {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profilePicture {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.profilePicture:hover {
    transform: scale(1.05);
}

.profileFormSection {
    flex: 1;
}

.formRow {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
}

@media screen and (max-width: 576px) {
    .formRow {
        flex-direction: column;
        gap: 16px;
        margin-bottom: 12px;
    }
}

/* Buttons */
.primaryButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.primaryButton:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

.primaryButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.secondaryButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background: white;
    color: #3b82f6;
    border: 2px solid #3b82f6;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondaryButton:hover:not(:disabled) {
    background: rgba(59, 130, 246, 0.1);
}

.secondaryButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.buttonGroup {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

@media screen and (max-width: 576px) {
    .buttonGroup {
        flex-direction: column;
        gap: 8px;
    }
}

/* Tabs */
.tabContainer {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 16px;
}

@media screen and (max-width: 576px) {
    .tabContainer {
        gap: 8px;
        overflow-x: auto;
        padding-bottom: 12px;
        margin-bottom: 16px;
    }
}

.tabButton {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tabButton:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.tabButtonActive {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    position: relative;
}

.tabButtonActive::after {
    content: '';
    position: absolute;
    bottom: -18px;
    left: 0;
    width: 100%;
    height: 3px;
    background: #3b82f6;
    border-radius: 3px;
}

/* 2FA Section */
.twoFASetup {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.twoFAInfo {
    display: flex;
    gap: 16px;
    padding: 20px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 12px;
}

.infoIcon {
    font-size: 24px;
    color: #3b82f6;
}

.infoContent {
    flex: 1;
}

.infoTitle {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.infoText {
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
}

.twoFAVerification {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Desktop: Setup methods container for side-by-side layout */
.setupMethodsContainer {
    display: flex;
    gap: 32px;
    align-items: flex-start;
}

@media screen and (max-width: 576px) {
    .twoFAVerification {
        gap: 24px;
    }

    .setupMethodsContainer {
        flex-direction: column;
        align-items: center;
        gap: 24px;
    }
}

.qrSection {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

/* Method Separator Styles */
.methodSeparator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    min-height: 200px;
}

.separatorLine {
    width: 1px;
    height: 60px;
    background: linear-gradient(to bottom, transparent, #e2e8f0, transparent);
}

.separatorText {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    background: white;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #e2e8f0;
    margin: 8px 0;
}

@media screen and (max-width: 576px) {
    .methodSeparator {
        flex-direction: row;
        padding: 20px 0;
        min-height: auto;
        width: 100%;
    }

    .separatorLine {
        width: 60px;
        height: 1px;
        background: linear-gradient(to right, transparent, #e2e8f0, transparent);
    }

    .separatorText {
        margin: 0 8px;
    }
}

.qrTitle {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.qrDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0 0 16px 0;
    line-height: 1.4;
}

.qrContainer {
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 16px 0;
}

.verificationSection {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    background: rgba(34, 197, 94, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(34, 197, 94, 0.1);
    margin-top: 16px;
}

@media screen and (max-width: 576px) {
    .verificationSection {
        margin-top: 0;
        padding: 16px;
    }
}

/* File Input */
.fileInput {
    display: none;
}

.fileInputLabel {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #3b82f6;
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.fileInputLabel:hover {
    background: #2563eb;
}

.fileInputHint {
    font-size: 12px;
    color: #64748b;
    margin: 8px 0 0 0;
}

/* Form Actions */
.formActions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e2e8f0;
}

@media screen and (max-width: 576px) {
    .formActions {
        margin-top: 16px;
        padding-top: 16px;
    }

    .formActions button {
        width: 100%;
    }
}

/* Delete Account Styles */
.dangerZone {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dangerInfo {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
}

.warningIcon {
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.warningContent {
    flex: 1;
}

.warningTitle {
    font-size: 16px;
    font-weight: 600;
    color: #dc2626;
    margin: 0 0 8px 0;
}

.warningText {
    font-size: 14px;
    color: #7f1d1d;
    margin: 0;
    line-height: 1.5;
}

.dangerButton {
    align-self: flex-start;
    padding: 12px 24px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.dangerButton:hover:not(:disabled) {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
    transform: translateY(-1px);
}

.dangerButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

@media (max-width: 576px) {
    .dangerZone {
        gap: 16px;
    }

    .dangerInfo {
        padding: 12px;
        gap: 10px;
    }

    .warningIcon {
        font-size: 18px;
    }

    .warningTitle {
        font-size: 15px;
    }

    .warningText {
        font-size: 13px;
    }

    .dangerButton {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
    }
}

/* Secret Section Styles */
.secretSection {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.secretTitle {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.secretDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0 0 16px 0;
    line-height: 1.4;
}

.secretContainer {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.secretDisplay {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.secretCode {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    background: #f8fafc;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    word-break: break-all;
    letter-spacing: 1px;
}

.copyButton {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.copyButton:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.copyButton:active {
    transform: translateY(0);
}

.copyIcon {
    font-size: 14px;
}

.secretHint {
    font-size: 13px;
    color: #64748b;
    text-align: center;
    margin: 0;
    line-height: 1.4;
}

@media screen and (max-width: 576px) {
    .secretSection {
        margin-top: 0;
        padding: 16px;
        background: rgba(59, 130, 246, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .secretTitle {
        font-size: 15px;
    }

    .secretDescription {
        font-size: 13px;
    }

    .secretDisplay {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .secretCode {
        font-size: 12px;
        text-align: center;
        padding: 10px;
    }

    .copyButton {
        width: 100%;
        justify-content: center;
        padding: 10px 16px;
    }
}

/* Disable 2FA Button - Professional red gradient for destructive action */
.disable2FAButton {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 50%, #dc2626 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 14px rgba(239, 68, 68, 0.25);
    position: relative;
    overflow: hidden;
    text-align: center;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
}

.disable2FAButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.disable2FAButton:hover::before {
    left: 100%;
}

.disable2FAButton:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.35);
}

.disable2FAButton:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 14px rgba(239, 68, 68, 0.25);
}

.disable2FAButton:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(156, 163, 175, 0.2);
}

.disable2FAButton:disabled::before {
    display: none;
}